from datetime import datetime, date
from typing import Optional
from pydantic import BaseModel


class StockDetailsBase(BaseModel):
    trading_code_id: int
    open_price: Optional[float] = None
    pe_ratio: Optional[float] = None
    record_date: Optional[date] = None
    dividend_info: Optional[str] = None


class StockDetailsCreate(StockDetailsBase):
    pass


class StockDetailsUpdate(BaseModel):
    open_price: Optional[float] = None
    pe_ratio: Optional[float] = None
    record_date: Optional[date] = None
    dividend_info: Optional[str] = None


class StockDetailsInDBBase(StockDetailsBase):
    id: int
    last_updated: datetime
    created_at: datetime

    class Config:
        from_attributes = True


class StockDetails(StockDetailsInDBBase):
    pass


class StockDetailsResponse(StockDetailsInDBBase):
    pass


class StockDetailsWithTradingCode(StockDetailsResponse):
    trading_code: Optional[str] = None
