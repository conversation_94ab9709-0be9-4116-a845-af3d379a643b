import asyncio
import logging
import re
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple

import requests
from bs4 import BeautifulSoup
from app.api.deps import get_db
from app.db.session import async_session
from app.models.stock_details import StockDetails
from app.models.trading_code import TradingCode
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

# Logger
logger = logging.getLogger(__name__)

# Login credentials
ECOSOFT_LOGIN_URL = "https://ost.ecosoftbd.com/Login"
ECOSOFT_ANALYSIS_URL = "https://ost.ecosoftbd.com/Analysis"
LOGIN_ID = "tazbinur"
PASSWORD = ".djpw.djpw"


class EcosoftScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        })
        self.is_logged_in = False

    def login(self) -> bool:
        """
        Login to ecosoftbd.com
        """
        try:
            logger.info("Attempting to login to ecosoftbd.com")
            
            # First, get the login page to extract any necessary tokens
            login_page = self.session.get(ECOSOFT_LOGIN_URL, timeout=30)
            if login_page.status_code != 200:
                logger.error(f"Failed to access login page: {login_page.status_code}")
                return False

            soup = BeautifulSoup(login_page.content, 'html.parser')
            
            # Look for any hidden form fields (like CSRF tokens)
            form = soup.find('form')
            form_data = {
                'LoginId': LOGIN_ID,
                'Password': PASSWORD
            }
            
            # Add any hidden input fields
            if form:
                hidden_inputs = form.find_all('input', type='hidden')
                for hidden_input in hidden_inputs:
                    name = hidden_input.get('name')
                    value = hidden_input.get('value', '')
                    if name:
                        form_data[name] = value

            # Submit login form
            login_response = self.session.post(
                ECOSOFT_LOGIN_URL,
                data=form_data,
                timeout=30,
                allow_redirects=True
            )

            # Check if login was successful
            if login_response.status_code == 200:
                # Check if we're redirected to a dashboard or if login page still shows
                if "dashboard" in login_response.url.lower() or "analysis" in login_response.url.lower():
                    logger.info("Successfully logged in to ecosoftbd.com")
                    self.is_logged_in = True
                    return True
                else:
                    # Try to access the analysis page to verify login
                    analysis_response = self.session.get(ECOSOFT_ANALYSIS_URL, timeout=30)
                    if analysis_response.status_code == 200 and "login" not in analysis_response.url.lower():
                        logger.info("Successfully logged in to ecosoftbd.com")
                        self.is_logged_in = True
                        return True

            logger.error("Login failed - credentials may be incorrect or site structure changed")
            return False

        except Exception as e:
            logger.error(f"Error during login: {str(e)}")
            return False

    def fetch_stock_data(self, trading_code: str) -> Optional[Dict]:
        """
        Fetch stock data for a specific trading code from the analysis page
        """
        if not self.is_logged_in:
            logger.error("Not logged in to ecosoftbd.com")
            return None

        try:
            logger.info(f"Fetching stock data for {trading_code}")
            
            # Access the analysis page
            response = self.session.get(ECOSOFT_ANALYSIS_URL, timeout=30)
            if response.status_code != 200:
                logger.error(f"Failed to access analysis page: {response.status_code}")
                return None

            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for the trading code in the page
            # This will need to be adjusted based on the actual structure of the analysis page
            stock_data = {
                'trading_code': trading_code,
                'open_price': None,
                'pe_ratio': None,
                'record_date': None,
                'dividend_info': None
            }

            # Try to find the stock data in tables or specific elements
            # This is a generic approach - may need adjustment based on actual page structure
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        # Look for the trading code in the first cell
                        first_cell_text = cells[0].get_text(strip=True).upper()
                        if trading_code.upper() in first_cell_text:
                            # Extract data from subsequent cells
                            try:
                                # This structure will need to be adjusted based on actual page layout
                                if len(cells) > 1:
                                    open_text = cells[1].get_text(strip=True)
                                    if open_text and open_text.replace('.', '').replace(',', '').isdigit():
                                        stock_data['open_price'] = float(open_text.replace(',', ''))
                                
                                if len(cells) > 2:
                                    pe_text = cells[2].get_text(strip=True)
                                    if pe_text and pe_text.replace('.', '').replace(',', '').isdigit():
                                        stock_data['pe_ratio'] = float(pe_text.replace(',', ''))
                                
                                if len(cells) > 3:
                                    date_text = cells[3].get_text(strip=True)
                                    # Try to parse date
                                    stock_data['record_date'] = self._parse_date(date_text)
                                
                                if len(cells) > 4:
                                    dividend_text = cells[4].get_text(strip=True)
                                    if dividend_text:
                                        stock_data['dividend_info'] = dividend_text[:255]  # Limit to 255 chars
                                
                                logger.info(f"Found data for {trading_code}: {stock_data}")
                                return stock_data
                            except Exception as e:
                                logger.error(f"Error parsing data for {trading_code}: {str(e)}")
                                continue

            logger.warning(f"No data found for {trading_code} on analysis page")
            return stock_data  # Return with None values

        except Exception as e:
            logger.error(f"Error fetching stock data for {trading_code}: {str(e)}")
            return None

    def _parse_date(self, date_text: str) -> Optional[date]:
        """
        Parse date from various formats
        """
        if not date_text or date_text.strip() == '':
            return None
        
        # Common date formats to try
        date_formats = [
            '%Y-%m-%d',
            '%d-%m-%Y',
            '%d/%m/%Y',
            '%m/%d/%Y',
            '%d-%b-%Y',
            '%d %b %Y',
            '%Y-%m-%d %H:%M:%S'
        ]
        
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_text.strip(), fmt).date()
                return parsed_date
            except ValueError:
                continue
        
        logger.warning(f"Could not parse date: {date_text}")
        return None

    def close(self):
        """
        Close the session
        """
        self.session.close()


async def fetch_and_update_stock_details(trading_codes: List[str] = None) -> Dict[str, bool]:
    """
    Fetch and update stock details for given trading codes or all trading codes
    """
    results = {}
    scraper = EcosoftScraper()
    
    try:
        # Login to ecosoftbd.com
        if not scraper.login():
            logger.error("Failed to login to ecosoftbd.com")
            return results

        async with async_session() as db:
            # Get trading codes to update
            if trading_codes is None:
                # Get all trading codes
                result = await db.execute(select(TradingCode))
                trading_code_objects = result.scalars().all()
                codes_to_update = [tc.code for tc in trading_code_objects]
            else:
                codes_to_update = trading_codes

            logger.info(f"Updating stock details for {len(codes_to_update)} trading codes")

            for code in codes_to_update:
                try:
                    # Fetch stock data
                    stock_data = scraper.fetch_stock_data(code)
                    if stock_data is None:
                        results[code] = False
                        continue

                    # Get trading code ID
                    result = await db.execute(
                        select(TradingCode).where(TradingCode.code == code.upper())
                    )
                    trading_code_obj = result.scalar_one_or_none()
                    
                    if not trading_code_obj:
                        logger.warning(f"Trading code {code} not found in database")
                        results[code] = False
                        continue

                    # Check if stock details already exist
                    result = await db.execute(
                        select(StockDetails).where(StockDetails.trading_code_id == trading_code_obj.id)
                    )
                    existing_details = result.scalar_one_or_none()

                    if existing_details:
                        # Update existing record
                        existing_details.open_price = stock_data.get('open_price')
                        existing_details.pe_ratio = stock_data.get('pe_ratio')
                        existing_details.record_date = stock_data.get('record_date')
                        existing_details.dividend_info = stock_data.get('dividend_info')
                        existing_details.last_updated = datetime.now()
                    else:
                        # Create new record
                        new_details = StockDetails(
                            trading_code_id=trading_code_obj.id,
                            open_price=stock_data.get('open_price'),
                            pe_ratio=stock_data.get('pe_ratio'),
                            record_date=stock_data.get('record_date'),
                            dividend_info=stock_data.get('dividend_info'),
                            last_updated=datetime.now(),
                            created_at=datetime.now()
                        )
                        db.add(new_details)

                    await db.commit()
                    results[code] = True
                    logger.info(f"Updated stock details for {code}")

                    # Small delay to avoid overwhelming the server
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"Error updating stock details for {code}: {str(e)}")
                    results[code] = False
                    await db.rollback()

    except Exception as e:
        logger.error(f"Error in fetch_and_update_stock_details: {str(e)}")
    finally:
        scraper.close()

    return results
