"use client";

import { SessionProvider } from "next-auth/react";
import ErrorBoundary from "./error-boundary";
import { AuthContextProvider } from "@/context/auth-context";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <div>Something went wrong. Please try refreshing the page.</div>
      }
    >
      <SessionProvider>
        <AuthContextProvider>
          {children}
        </AuthContextProvider>
      </SessionProvider>
    </ErrorBoundary>
  );
}
