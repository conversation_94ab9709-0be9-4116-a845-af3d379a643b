# This file initializes all models in the correct order
# to avoid circular import issues

# First, import the base class
from app.db.base_class import Base
# Import models that depend on User
from app.models.portfolio import Portfolio
# Import models that depend on Portfolio
from app.models.portfolio_entry import PortfolioEntry
# Import models without relationships to other models
from app.models.stock_details import StockDetails
from app.models.trading_code import TradingCode
# Import User model
from app.models.user import User
# Define relationships between models
from sqlalchemy.orm import relationship

# Add the portfolios relationship to User after Portfolio is defined
User.portfolios = relationship(
    "Portfolio",
    foreign_keys="Portfolio.user_id",
    cascade="all, delete-orphan",
    lazy="selectin"
)

# Return all models for use in other modules
__all__ = ['Base', 'TradingCode', 'User', 'Portfolio', 'PortfolioEntry', 'StockDetails']
