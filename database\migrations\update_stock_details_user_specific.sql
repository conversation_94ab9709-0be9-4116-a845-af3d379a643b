-- Add user_id to stock_details table to make it user-specific
ALTER TABLE dse_schema.stock_details 
ADD COLUMN user_id INTEGER NOT NULL REFERENCES dse_schema.users(id) ON DELETE CASCADE;

-- Drop the old unique constraint
ALTER TABLE dse_schema.stock_details 
DROP CONSTRAINT stock_details_trading_code_id_key;

-- Add new unique constraint for user_id + trading_code_id combination
ALTER TABLE dse_schema.stock_details 
ADD CONSTRAINT stock_details_user_trading_code_unique 
UNIQUE(user_id, trading_code_id);

-- Create index for user_id
CREATE INDEX IF NOT EXISTS idx_stock_details_user_id ON dse_schema.stock_details(user_id);

-- Add comment
COMMENT ON COLUMN dse_schema.stock_details.user_id IS 'User who owns this stock in their portfolio';
