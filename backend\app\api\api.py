from app.api.endpoints import (admin, auth, live_prices, stock_details,
                               trading, users, websocket)
from fastapi import APIRouter

api_router = APIRouter()

# Auth endpoints
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# User endpoints
api_router.include_router(users.router, prefix="/users", tags=["users"])

# Admin endpoints
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])

# Trading endpoints
api_router.include_router(trading.router, prefix="/trading-codes", tags=["trading"])

# Live prices endpoints
api_router.include_router(live_prices.router, prefix="/live-prices", tags=["live-prices"])

# Stock details endpoints
api_router.include_router(stock_details.router, prefix="/stock-details", tags=["stock-details"])

# WebSocket endpoints - no prefix to avoid path conflicts
api_router.include_router(websocket.router, tags=["websocket"])

@api_router.get("/health")
async def health_check():
    return {"status": "ok"}

