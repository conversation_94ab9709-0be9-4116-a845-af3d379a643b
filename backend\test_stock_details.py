import sys
sys.path.append('.')
import asyncio
from app.core.ecosoft_scraper import EcosoftScraper
from app.services.portfolio_stock_details import sync_user_stock_details
from app.db.session import async_session

async def test_scraper():
    """Test the ecosoft scraper"""
    print("Testing EcoSoft scraper...")
    
    scraper = EcosoftScraper()
    
    # Test login
    print("Testing login...")
    login_success = scraper.login()
    print(f"Login successful: {login_success}")
    
    if login_success:
        # Test fetching data for a sample trading code
        test_codes = ["AAPL", "GOOGL", "MSFT"]  # These are just test codes
        
        for code in test_codes:
            print(f"\nTesting data fetch for {code}...")
            data = scraper.fetch_stock_data(code)
            print(f"Data for {code}: {data}")
    
    scraper.close()

async def test_user_stock_details():
    """Test user-specific stock details sync"""
    print("\nTesting user-specific stock details sync...")
    
    # Test with a mock user ID (you can change this to a real user ID)
    test_user_id = 1
    
    async with async_session() as db:
        try:
            result = await sync_user_stock_details(db, test_user_id)
            print(f"Sync result: {result}")
        except Exception as e:
            print(f"Error during sync: {e}")

async def main():
    print("=== Stock Details Implementation Test ===\n")
    
    # Test 1: Scraper functionality
    await test_scraper()
    
    # Test 2: User-specific stock details
    await test_user_stock_details()
    
    print("\n=== Test completed ===")

if __name__ == "__main__":
    asyncio.run(main())
