import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Set

from app.core.ecosoft_scraper import fetch_and_update_stock_details
from app.models.portfolio_entry import PortfolioEntry
from app.models.stock_details import StockDetails
from app.models.trading_code import TradingCode
from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


async def get_user_portfolio_trading_codes(db: AsyncSession, user_id: int) -> Set[str]:
    """
    Get all trading codes that are currently in the user's portfolio (with positive holdings)
    """
    try:
        # Import Portfolio model here to avoid circular imports
        from app.models.portfolio import Portfolio

        # Get all portfolio entries for the user through their portfolio
        entries_query = (
            select(PortfolioEntry, TradingCode.code)
            .join(TradingCode, PortfolioEntry.trading_code_id == TradingCode.id)
            .join(Portfolio, PortfolioEntry.portfolio_id == Portfolio.id)
            .where(Portfolio.user_id == user_id)
        )

        result = await db.execute(entries_query)
        entries_with_codes = result.all()

        # Group by trading code and calculate net holdings
        holdings = {}
        for entry, trading_code in entries_with_codes:
            if trading_code not in holdings:
                holdings[trading_code] = 0

            if entry.transaction_type == "buy":
                holdings[trading_code] += entry.unit_count
            elif entry.transaction_type == "sell":
                holdings[trading_code] -= entry.unit_count

        # Return only trading codes with positive holdings
        return {code for code, count in holdings.items() if count > 0}

    except Exception as e:
        logger.error(f"Error getting user portfolio trading codes: {str(e)}")
        return set()


async def sync_user_stock_details(db: AsyncSession, user_id: int) -> dict:
    """
    Sync stock details for a user's current portfolio holdings
    """
    try:
        # Get current portfolio trading codes
        current_codes = await get_user_portfolio_trading_codes(db, user_id)

        if not current_codes:
            logger.info(f"No portfolio holdings found for user {user_id}")
            # Clean up any existing stock details for this user
            await db.execute(
                delete(StockDetails).where(StockDetails.user_id == user_id)
            )
            await db.commit()
            return {"message": "No holdings found, cleaned up stock details"}

        # Get existing stock details for this user
        existing_query = (
            select(StockDetails, TradingCode.code)
            .join(TradingCode, StockDetails.trading_code_id == TradingCode.id)
            .where(StockDetails.user_id == user_id)
        )
        result = await db.execute(existing_query)
        existing_details = result.all()
        existing_codes = {trading_code for _, trading_code in existing_details}

        # Determine which codes need to be added and which need to be removed
        codes_to_add = current_codes - existing_codes
        codes_to_remove = existing_codes - current_codes

        # Remove stock details for codes no longer in portfolio
        if codes_to_remove:
            logger.info(f"Removing stock details for codes no longer in portfolio: {codes_to_remove}")
            for code in codes_to_remove:
                # Get trading code ID
                tc_result = await db.execute(
                    select(TradingCode.id).where(TradingCode.code == code)
                )
                trading_code_id = tc_result.scalar_one_or_none()
                if trading_code_id:
                    await db.execute(
                        delete(StockDetails).where(
                            StockDetails.user_id == user_id,
                            StockDetails.trading_code_id == trading_code_id
                        )
                    )

        # Check which existing codes need immediate update (older than 24 hours)
        codes_needing_update = set()
        now = datetime.now()
        for stock_detail, trading_code in existing_details:
            if trading_code in current_codes:
                age = now - stock_detail.last_updated.replace(tzinfo=None)
                if age > timedelta(hours=24):
                    codes_needing_update.add(trading_code)

        # Add codes that need immediate update to codes_to_add
        codes_to_fetch = codes_to_add | codes_needing_update

        # Fetch stock details for new codes and codes needing update
        results = {}
        if codes_to_fetch:
            logger.info(f"Fetching stock details for codes: {codes_to_fetch}")

            # Use the ecosoft scraper to fetch data
            fetch_results = await fetch_and_update_stock_details_for_user(
                db, user_id, list(codes_to_fetch)
            )
            results.update(fetch_results)

        await db.commit()

        return {
            "message": f"Stock details synced for user {user_id}",
            "current_codes": list(current_codes),
            "codes_added": list(codes_to_add),
            "codes_removed": list(codes_to_remove),
            "codes_updated": list(codes_needing_update),
            "fetch_results": results
        }

    except Exception as e:
        logger.error(f"Error syncing stock details for user {user_id}: {str(e)}")
        await db.rollback()
        return {"error": str(e)}


async def fetch_and_update_stock_details_for_user(
    db: AsyncSession, user_id: int, trading_codes: List[str]
) -> dict:
    """
    Fetch and update stock details for specific trading codes for a specific user
    """
    results = {}

    try:
        # Import here to avoid circular imports
        from app.core.ecosoft_scraper import EcosoftScraper

        scraper = EcosoftScraper()

        # Login to ecosoftbd.com
        if not scraper.login():
            logger.error("Failed to login to ecosoftbd.com")
            return {code: False for code in trading_codes}

        for code in trading_codes:
            try:
                # Fetch stock data
                stock_data = scraper.fetch_stock_data(code)
                if stock_data is None:
                    results[code] = False
                    continue

                # Get trading code ID
                tc_result = await db.execute(
                    select(TradingCode.id).where(TradingCode.code == code.upper())
                )
                trading_code_id = tc_result.scalar_one_or_none()

                if not trading_code_id:
                    logger.warning(f"Trading code {code} not found in database")
                    results[code] = False
                    continue

                # Check if stock details already exist for this user and trading code
                existing_result = await db.execute(
                    select(StockDetails).where(
                        StockDetails.user_id == user_id,
                        StockDetails.trading_code_id == trading_code_id
                    )
                )
                existing_details = existing_result.scalar_one_or_none()

                if existing_details:
                    # Update existing record
                    existing_details.open_price = stock_data.get('open_price')
                    existing_details.pe_ratio = stock_data.get('pe_ratio')
                    existing_details.record_date = stock_data.get('record_date')
                    existing_details.dividend_info = stock_data.get('dividend_info')
                    existing_details.last_updated = datetime.now()
                else:
                    # Create new record
                    new_details = StockDetails(
                        user_id=user_id,
                        trading_code_id=trading_code_id,
                        open_price=stock_data.get('open_price'),
                        pe_ratio=stock_data.get('pe_ratio'),
                        record_date=stock_data.get('record_date'),
                        dividend_info=stock_data.get('dividend_info'),
                        last_updated=datetime.now(),
                        created_at=datetime.now()
                    )
                    db.add(new_details)

                results[code] = True
                logger.info(f"Updated stock details for {code} for user {user_id}")

                # Small delay to avoid overwhelming the server
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error updating stock details for {code}: {str(e)}")
                results[code] = False

        scraper.close()

    except Exception as e:
        logger.error(f"Error in fetch_and_update_stock_details_for_user: {str(e)}")
        results = {code: False for code in trading_codes}

    return results


async def check_and_update_stock_details_on_portfolio_change(
    db: AsyncSession, user_id: int
) -> dict:
    """
    Check and update stock details when portfolio changes occur
    This should be called after any portfolio entry is added or removed
    """
    return await sync_user_stock_details(db, user_id)
