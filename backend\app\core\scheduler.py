import asyncio
import logging
import time
from datetime import datetime
from datetime import time as dt_time
from datetime import timedelta
from typing import Dict

import pytz
from app.api.deps import get_db
from app.db.session import async_session
from app.schemas.live_price import LivePrice

# Logger
logger = logging.getLogger(__name__)

# Bangladesh timezone
BST = pytz.timezone('Asia/Dhaka')

# Market hours (10:00 AM to 2:30 PM BST)
MARKET_OPEN_TIME = dt_time(10, 0)
MARKET_CLOSE_TIME = dt_time(14, 30)

# Weekend days (Friday and Saturday in Bangladesh)
WEEKEND_DAYS = [4, 5]  # 4=Friday, 5=Saturday in Python's datetime (0=Monday)

# Cache for live prices
live_prices_cache: Dict[str, LivePrice] = {}

# Flag to track if the background task is running
is_background_task_running = False

# Stock details are now handled per-user, not globally

# Last time the cache was accessed
last_cache_access_time = time.time()


async def schedule_live_price_updates():
    """
    Schedule background task to update live prices intelligently based on market hours
    """
    from app.api.endpoints.live_prices import update_live_prices_task

    try:
        while True:
            try:
                # Get current time in Bangladesh
                now_bst = datetime.now(BST)
                current_time = now_bst.time()
                current_weekday = now_bst.weekday()

                # Check if market is open (weekday and within market hours)
                is_weekend = current_weekday in WEEKEND_DAYS
                is_market_hours = MARKET_OPEN_TIME <= current_time <= MARKET_CLOSE_TIME
                is_market_open = not is_weekend and is_market_hours

                # Check if the cache has been accessed recently (within the last 15 minutes)
                current_unix_time = time.time()
                cache_age = current_unix_time - last_cache_access_time

                if cache_age < 15 * 60:  # 15 minutes
                    if is_market_open:
                        logger.info(f"Running scheduled live price update during market hours (cache accessed {cache_age:.0f} seconds ago)")
                        async with async_session() as db:
                            await update_live_prices_task(db)
                    else:
                        logger.info(f"Skipping live price update (market closed: {'weekend' if is_weekend else 'outside trading hours'})")
                else:
                    logger.info(f"Skipping live price update (cache not accessed for {cache_age:.0f} seconds)")

                # Stock details are now handled per-user, not globally
                # No global stock details updates needed

                # Determine next sleep duration
                if is_market_open:
                    # During market hours, poll every 5 minutes
                    sleep_duration = 5 * 60
                else:
                    # Outside market hours, check again in 30 minutes
                    sleep_duration = 30 * 60

                logger.info(f"Next price check in {sleep_duration/60:.0f} minutes")
                # Use wait_for with shield=False to allow cancellation
                try:
                    await asyncio.wait_for(asyncio.sleep(sleep_duration), timeout=sleep_duration)
                except asyncio.CancelledError:
                    logger.info("Live price update task cancelled during sleep")
                    raise
                except asyncio.TimeoutError:
                    # This should not happen as the timeout equals the sleep duration
                    pass

            except asyncio.CancelledError:
                logger.info("Live price update task cancelled")
                raise
            except Exception as e:
                logger.error(f"Error in scheduled task: {str(e)}")
                # Wait a bit before retrying
                try:
                    await asyncio.wait_for(asyncio.sleep(30), timeout=30)
                except asyncio.CancelledError:
                    logger.info("Live price update task cancelled during error recovery")
                    raise
    except asyncio.CancelledError:
        logger.info("Live price update scheduler shutting down gracefully")


def update_cache_access_time():
    """
    Update the timestamp of the last cache access
    """
    global last_cache_access_time
    last_cache_access_time = time.time()
    logger.debug(f"Cache access time updated: {datetime.fromtimestamp(last_cache_access_time).isoformat()}")


# Stock details functions removed - now handled per-user in portfolio_stock_details service


# These functions are kept for backward compatibility but simplified
def add_active_user(user_id: str):
    """
    Record that a user is active (updates cache access time)
    """
    update_cache_access_time()
    logger.info(f"User {user_id} activity recorded")


def remove_active_user(user_id: str):
    """
    No-op function kept for backward compatibility
    """
    logger.info(f"User {user_id} activity ended")



