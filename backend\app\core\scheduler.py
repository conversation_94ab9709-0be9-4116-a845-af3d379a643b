import asyncio
import logging
import time
from datetime import datetime
from datetime import time as dt_time
from datetime import timedelta
from typing import Dict

import pytz
from app.api.deps import get_db
from app.db.session import async_session
from app.schemas.live_price import LivePrice

# Logger
logger = logging.getLogger(__name__)

# Bangladesh timezone
BST = pytz.timezone('Asia/Dhaka')

# Market hours (10:00 AM to 2:30 PM BST)
MARKET_OPEN_TIME = dt_time(10, 0)
MARKET_CLOSE_TIME = dt_time(14, 30)

# Weekend days (Friday and Saturday in Bangladesh)
WEEKEND_DAYS = [4, 5]  # 4=Friday, 5=Saturday in Python's datetime (0=Monday)

# Cache for live prices
live_prices_cache: Dict[str, LivePrice] = {}

# Flag to track if the background task is running
is_background_task_running = False

# Flag to track if stock details update is running
is_stock_details_update_running = False

# Last time stock details were updated
last_stock_details_update = None

# Last time the cache was accessed
last_cache_access_time = time.time()


async def schedule_live_price_updates():
    """
    Schedule background task to update live prices intelligently based on market hours
    """
    from app.api.endpoints.live_prices import update_live_prices_task

    try:
        while True:
            try:
                # Get current time in Bangladesh
                now_bst = datetime.now(BST)
                current_time = now_bst.time()
                current_weekday = now_bst.weekday()

                # Check if market is open (weekday and within market hours)
                is_weekend = current_weekday in WEEKEND_DAYS
                is_market_hours = MARKET_OPEN_TIME <= current_time <= MARKET_CLOSE_TIME
                is_market_open = not is_weekend and is_market_hours

                # Check if the cache has been accessed recently (within the last 15 minutes)
                current_unix_time = time.time()
                cache_age = current_unix_time - last_cache_access_time

                if cache_age < 15 * 60:  # 15 minutes
                    if is_market_open:
                        logger.info(f"Running scheduled live price update during market hours (cache accessed {cache_age:.0f} seconds ago)")
                        async with async_session() as db:
                            await update_live_prices_task(db)
                    else:
                        logger.info(f"Skipping live price update (market closed: {'weekend' if is_weekend else 'outside trading hours'})")
                else:
                    logger.info(f"Skipping live price update (cache not accessed for {cache_age:.0f} seconds)")

                # Handle stock details updates
                if should_update_stock_details():
                    logger.info("Scheduled time for stock details update (10:30 AM Bangladesh time)")
                    await update_stock_details_task()
                elif needs_immediate_stock_details_update():
                    logger.info("Stock details need immediate update (not updated in last 24 hours)")
                    await update_stock_details_task()

                # Determine next sleep duration
                if is_market_open:
                    # During market hours, poll every 5 minutes
                    sleep_duration = 5 * 60
                else:
                    # Outside market hours, check again in 30 minutes
                    sleep_duration = 30 * 60

                logger.info(f"Next price check in {sleep_duration/60:.0f} minutes")
                # Use wait_for with shield=False to allow cancellation
                try:
                    await asyncio.wait_for(asyncio.sleep(sleep_duration), timeout=sleep_duration)
                except asyncio.CancelledError:
                    logger.info("Live price update task cancelled during sleep")
                    raise
                except asyncio.TimeoutError:
                    # This should not happen as the timeout equals the sleep duration
                    pass

            except asyncio.CancelledError:
                logger.info("Live price update task cancelled")
                raise
            except Exception as e:
                logger.error(f"Error in scheduled task: {str(e)}")
                # Wait a bit before retrying
                try:
                    await asyncio.wait_for(asyncio.sleep(30), timeout=30)
                except asyncio.CancelledError:
                    logger.info("Live price update task cancelled during error recovery")
                    raise
    except asyncio.CancelledError:
        logger.info("Live price update scheduler shutting down gracefully")


def update_cache_access_time():
    """
    Update the timestamp of the last cache access
    """
    global last_cache_access_time
    last_cache_access_time = time.time()
    logger.debug(f"Cache access time updated: {datetime.fromtimestamp(last_cache_access_time).isoformat()}")


async def update_stock_details_task():
    """
    Background task to update stock details from ecosoftbd.com
    """
    global is_stock_details_update_running, last_stock_details_update

    if is_stock_details_update_running:
        logger.info("Stock details update already running, skipping")
        return

    is_stock_details_update_running = True

    try:
        logger.info("Starting stock details update from ecosoftbd.com")

        # Import here to avoid circular imports
        from app.core.ecosoft_scraper import fetch_and_update_stock_details

        # Fetch and update all stock details
        results = await fetch_and_update_stock_details()

        # Update last update time
        last_stock_details_update = datetime.now(BST)

        successful_updates = sum(1 for success in results.values() if success)
        total_codes = len(results)

        logger.info(f"Stock details update completed: {successful_updates}/{total_codes} successful")

    except Exception as e:
        logger.error(f"Error in stock details update task: {str(e)}")
    finally:
        is_stock_details_update_running = False


def should_update_stock_details() -> bool:
    """
    Check if stock details should be updated based on schedule and last update time
    """
    now = datetime.now(BST)

    # Check if it's a weekday (Sunday to Thursday in Bangladesh)
    if now.weekday() in [4, 5]:  # Friday and Saturday
        return False

    # Check if it's 10:30 AM Bangladesh time
    target_time = dt_time(10, 30)
    current_time = now.time()

    # Allow update between 10:30 and 10:35 AM
    if not (target_time <= current_time <= dt_time(10, 35)):
        return False

    # Check if we already updated today
    if last_stock_details_update:
        last_update_date = last_stock_details_update.date()
        today = now.date()
        if last_update_date == today:
            return False

    return True


def needs_immediate_stock_details_update() -> bool:
    """
    Check if stock details need immediate update (if not updated in last 24 hours)
    """
    if last_stock_details_update is None:
        return True

    now = datetime.now(BST)
    time_since_update = now - last_stock_details_update

    # If more than 24 hours since last update, update immediately
    return time_since_update > timedelta(hours=24)


# These functions are kept for backward compatibility but simplified
def add_active_user(user_id: str):
    """
    Record that a user is active (updates cache access time)
    """
    update_cache_access_time()
    logger.info(f"User {user_id} activity recorded")


def remove_active_user(user_id: str):
    """
    No-op function kept for backward compatibility
    """
    logger.info(f"User {user_id} activity ended")



