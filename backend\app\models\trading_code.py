from app.db.base_class import Base
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship


class TradingCode(Base):
    __tablename__ = 'trading_codes'
    __table_args__ = {'schema': 'dse_schema'}

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)

    # Relationships
    portfolio_entries = relationship("PortfolioEntry", back_populates="trading_code", lazy="selectin")
    stock_details = relationship("StockDetails", back_populates="trading_code", uselist=False, lazy="selectin")
