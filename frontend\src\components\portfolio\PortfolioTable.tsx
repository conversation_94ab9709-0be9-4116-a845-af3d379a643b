"use client";

import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>pDown, Minus, Plus, Search } from "lucide-react";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  <PERSON>alog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";

// Portfolio holding type
export interface PortfolioHolding {
  tradingCode: string;
  currentPrice: number;
  shareCount: number;
  avgPrice: number;
  totalInvestment: number;
  lastBuyDate: string; // This is now the last transaction date, but keeping the name for compatibility
  isLivePrice?: boolean;
  previousPrice?: number;
  changeDirection?: "up" | "down" | "none";
  changePercent?: number;
  gainLoss?: number; // Add this property
}

// Trading code type
interface TradingCode {
  id: number;
  code: string;
  name: string;
}

interface PortfolioTableProps {
  holdings: PortfolioHolding[];
  isLoading?: boolean;
  onEntryAdded?: () => void;
  investmentAmount?: number; // Add this prop
}

type SortField = keyof PortfolioHolding;
type SortDirection = "asc" | "desc";

export default function PortfolioTable({
  holdings,
  isLoading: tableIsLoading = false,
  onEntryAdded,
  investmentAmount = 0, // Default to 0
}: PortfolioTableProps) {
  const { data: session } = useSession();
  const { toast } = useToast();

  // State for sorting
  const [sortField, setSortField] = useState<SortField>("tradingCode");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");

  // State for search
  const [searchQuery, setSearchQuery] = useState("");

  // State for buy modal
  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false);
  const [isSellModalOpen, setIsSellModalOpen] = useState(false);
  const [tradingCodes, setTradingCodes] = useState<TradingCode[]>([]);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [commission, setCommission] = useState<number | null>(null);

  // Form state for buy modal
  const [buyFormData, setBuyFormData] = useState({
    tradingCodeId: "",
    buyPrice: "",
    unitCount: "",
  });

  // Form state for sell modal
  const [sellFormData, setSellFormData] = useState({
    tradingCodeId: "",
    sellPrice: "",
    unitCount: "",
  });

  // Fetch trading codes with caching
  const fetchTradingCodes = useCallback(async () => {
    // Skip fetching if we already have trading codes
    if (tradingCodes.length > 0) {
      return;
    }

    try {
      // Check if we have cached trading codes
      const cachedData = localStorage.getItem("tradingCodes");
      const cachedTimestamp = localStorage.getItem("tradingCodesTimestamp");

      // If we have cached data and it's less than 7 days old, use it
      if (cachedData && cachedTimestamp) {
        const timestamp = parseInt(cachedTimestamp);
        const now = Date.now();
        const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;

        if (now - timestamp < sevenDaysInMs) {
          const parsedData = JSON.parse(cachedData);
          setTradingCodes(parsedData);
          console.log("Using cached trading codes");
          return;
        }
      }

      // If no cache or cache is expired, fetch from API
      console.log("Fetching trading codes from API");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/trading-codes`,
        {
          headers: {
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setTradingCodes(data);

        // Cache the data
        localStorage.setItem("tradingCodes", JSON.stringify(data));
        localStorage.setItem("tradingCodesTimestamp", Date.now().toString());
      } else {
        console.error("Failed to fetch trading codes:", await response.text());

        // If API call fails but we have cached data (even if expired), use it as fallback
        if (cachedData) {
          setTradingCodes(JSON.parse(cachedData));
          toast({
            title: "Using cached trading codes",
            description: "Could not fetch latest trading codes from server.",
          });
        }
      }
    } catch (error) {
      console.error("Error fetching trading codes:", error);

      // If there's an error but we have cached data, use it as fallback
      const cachedData = localStorage.getItem("tradingCodes");
      if (cachedData) {
        setTradingCodes(JSON.parse(cachedData));
        toast({
          title: "Using cached trading codes",
          description: "Could not fetch latest trading codes from server.",
        });
      }
    }
  }, [session, toast, tradingCodes]);

  // Fetch commission from API
  const fetchCommission = useCallback(async () => {
    // Skip if commission is already set
    if (commission !== null) {
      return;
    }

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`,
        {
          headers: {
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setCommission(data.commission);
      } else {
        console.error("Failed to fetch portfolio data:", await response.text());
      }
    } catch (error) {
      console.error("Error fetching portfolio data:", error);
    }
  }, [session, commission]);

  // Fetch trading codes and commission when component mounts (only once)
  useEffect(() => {
    if (session?.user?.accessToken) {
      fetchTradingCodes();
      fetchCommission();
    }
  }, [session]); // Only depend on session to prevent unnecessary re-fetches

  // Handle buy form input changes
  const handleBuyInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBuyFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle sell form input changes
  const handleSellInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSellFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle trading code selection for buy
  const handleTradingCodeSelect = (value: string) => {
    setBuyFormData((prev) => ({ ...prev, tradingCodeId: value }));
  };

  // Handle trading code selection for sell
  const handleSellTradingCodeSelect = (value: string) => {
    setSellFormData((prev) => ({ ...prev, tradingCodeId: value }));
  };

  // Handle buy form submission
  const handleBuySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !buyFormData.tradingCodeId ||
      !buyFormData.buyPrice ||
      !buyFormData.unitCount
    ) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all fields",
      });
      return;
    }

    setIsFormSubmitting(true);

    try {
      // Calculate final price with commission
      const buyPrice = parseFloat(buyFormData.buyPrice);
      const unitCount = parseInt(buyFormData.unitCount);
      const finalPrice = commission
        ? buyPrice * (1 + commission / 100)
        : buyPrice;

      // Round to 3 decimal places
      const roundedPrice = Math.round(finalPrice * 1000) / 1000;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            trading_code_id: parseInt(buyFormData.tradingCodeId),
            transaction_price: roundedPrice,
            unit_count: unitCount,
            transaction_date: new Date().toISOString(), // Full ISO datetime format
            transaction_type: "buy",
          }),
        }
      );

      if (response.ok) {
        toast({
          variant: "success",
          title: "Success",
          description: "Portfolio entry added successfully",
        });
        setIsBuyModalOpen(false);
        // Reset form data
        setBuyFormData({
          tradingCodeId: "",
          buyPrice: "",
          unitCount: "",
        });
        // Call the callback to refresh data instead of reloading the page
        if (onEntryAdded) {
          onEntryAdded();
        }
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to add portfolio entry",
        });
      }
    } catch (err) {
      console.error("Error adding portfolio entry:", err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while adding portfolio entry",
      });
    } finally {
      setIsFormSubmitting(false);
    }
  };

  // Handle sell form submission
  const handleSellSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !sellFormData.tradingCodeId ||
      !sellFormData.sellPrice ||
      !sellFormData.unitCount
    ) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all fields",
      });
      return;
    }

    setIsFormSubmitting(true);

    try {
      // Calculate final price with commission
      const sellPrice = parseFloat(sellFormData.sellPrice);
      const unitCount = parseInt(sellFormData.unitCount);
      const finalPrice = commission
        ? sellPrice * (1 - commission / 100)
        : sellPrice;

      // Round to 3 decimal places
      const roundedPrice = Math.round(finalPrice * 1000) / 1000;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            trading_code_id: parseInt(sellFormData.tradingCodeId),
            transaction_price: roundedPrice,
            unit_count: unitCount,
            transaction_date: new Date().toISOString(), // Full ISO datetime format
            transaction_type: "sell",
          }),
        }
      );

      if (response.ok) {
        toast({
          variant: "success",
          title: "Success",
          description: "Sell transaction recorded successfully",
        });
        setIsSellModalOpen(false);
        // Reset form data
        setSellFormData({
          tradingCodeId: "",
          sellPrice: "",
          unitCount: "",
        });
        // Call the callback to refresh data instead of reloading the page
        if (onEntryAdded) {
          onEntryAdded();
        }
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to record sell transaction",
        });
      }
    } catch (err) {
      console.error("Error recording sell transaction:", err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while recording sell transaction",
      });
    } finally {
      setIsFormSubmitting(false);
    }
  };

  // Function to format currency
  const formatCurrency = (amount: number) => {
    // Check if amount is a valid number
    if (amount === undefined || amount === null || isNaN(amount)) {
      return "BDT 0.00";
    }

    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "BDT",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Function to format currency for gain/loss
  const formatGainLoss = (amount: number) => {
    // Check if amount is a valid number
    if (amount === undefined || amount === null || isNaN(amount)) {
      return "BDT 0.00";
    }

    // Format without the negative sign
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "BDT",
      minimumFractionDigits: 2,
      signDisplay: "never", // Never show the sign
    }).format(Math.abs(amount));
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "N/A";
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Function to handle sorting
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Filter and sort the holdings
  const filteredAndSortedHoldings = useMemo(() => {
    // First filter by search query
    const filtered = holdings.filter((holding) => {
      if (!searchQuery) return true;

      const query = searchQuery.toLowerCase();

      // Search in all string fields
      return (
        holding.tradingCode.toLowerCase().includes(query) ||
        holding.currentPrice.toString().includes(query) ||
        holding.shareCount.toString().includes(query) ||
        holding.avgPrice.toString().includes(query) ||
        holding.totalInvestment.toString().includes(query) ||
        holding.lastBuyDate.includes(query)
      );
    });

    // Then sort
    return [...filtered].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      // Handle different types of values
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        // For numbers and dates (convert to numbers to ensure proper comparison)
        const aNum = typeof aValue === "number" ? aValue : Number(aValue);
        const bNum = typeof bValue === "number" ? bValue : Number(bValue);
        return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
      }
    });
  }, [holdings, searchQuery, sortField, sortDirection]);

  // Calculate total investment
  const totalInvestment = useMemo(() => {
    return holdings.reduce((sum, holding) => sum + holding.totalInvestment, 0);
  }, [holdings]);

  // Calculate remaining investment
  const remainingInvestment = useMemo(() => {
    return Math.max(0, investmentAmount - totalInvestment);
  }, [investmentAmount, totalInvestment]);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-xl font-bold">Your Portfolio</CardTitle>
          <CardDescription>
            Remaining Investment: {formatCurrency(remainingInvestment)}
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() => setIsBuyModalOpen(true)}
            disabled={commission === null || commission === 0}
            size="sm"
            className="flex items-center"
          >
            <Plus className="mr-1 h-4 w-4" /> Buy
          </Button>
          <Button
            onClick={() => setIsSellModalOpen(true)}
            disabled={
              commission === null || commission === 0 || holdings.length === 0
            }
            size="sm"
            variant="outline"
            className="flex items-center"
          >
            <Minus className="mr-1 h-4 w-4" /> Sell
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Search input */}
        <div className="flex items-center mb-4 relative">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by trading code or other values..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {tableIsLoading ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 border-4 border-t-blue-500 border-b-blue-500 rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-gray-500">Loading portfolio data...</p>
          </div>
        ) : filteredAndSortedHoldings.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p className="text-lg">No holdings found.</p>
            <p className="mt-2">
              {searchQuery
                ? "Try adjusting your search query."
                : "Your portfolio holdings will appear here once you add them."}
            </p>
          </div>
        ) : (
          <Table>
            <TableCaption>Your current stock holdings</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead
                  onClick={() => handleSort("tradingCode")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Trading Code
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "tradingCode" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("currentPrice")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Current Price
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "currentPrice" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("shareCount")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Share Count
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "shareCount" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("avgPrice")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Avg Price
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "avgPrice" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("totalInvestment")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Total Investment
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "totalInvestment" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-default"
                >
                  <div className="flex items-center">
                    Gain/Loss
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("lastBuyDate")}
                  className="cursor-pointer"
                >
                  <div className="flex items-center">
                    Last Buy Date
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                    {sortField === "lastBuyDate" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedHoldings.map((holding, index) => (
                <TableRow
                  key={index}
                  className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
                >
                  <TableCell className="font-medium">
                    {holding.tradingCode}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          {formatCurrency(holding.currentPrice)}
                          {holding.isLivePrice && (
                            <span
                              className="ml-2 h-2 w-2 rounded-full bg-green-500 animate-pulse"
                              title="Live price from DSE"
                            ></span>
                          )}
                        </div>

                        {holding.changeDirection &&
                          holding.changeDirection !== "none" && (
                            <div
                              className={`text-xs mt-1 ${
                                holding.changeDirection === "up"
                                  ? "text-green-600"
                                  : "text-red-600"
                              }`}
                            >
                              {holding.changeDirection === "up" ? "▲" : "▼"}
                              {holding.changePercent !== undefined &&
                                `${Math.abs(holding.changePercent).toFixed(
                                  2
                                )}%`}
                            </div>
                          )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{holding.shareCount}</TableCell>
                  <TableCell>{formatCurrency(holding.avgPrice)}</TableCell>
                  <TableCell>
                    {formatCurrency(holding.totalInvestment)}
                  </TableCell>
                  <TableCell>
                    {(() => {
                      // Calculate gain/loss with commission
                      const sellValue = holding.currentPrice * (1 - (commission || 0) / 100) * holding.shareCount;
                      const gainLoss = sellValue - holding.totalInvestment;
                      const isPositive = gainLoss >= 0;

                      return (
                        <span className={isPositive ? "text-green-600" : "text-red-600"}>
                          {formatGainLoss(gainLoss)}
                        </span>
                      );
                    })()}
                  </TableCell>
                  <TableCell>{formatDate(holding.lastBuyDate)}</TableCell>
                </TableRow>
              ))}

              {/* Summary Row with darker background */}
              {filteredAndSortedHoldings.length > 0 && (
                <TableRow className="font-medium bg-muted/90 border-t-4">
                  <TableCell colSpan={3} className="text-right">
                    Total:
                  </TableCell>
                  <TableCell>—</TableCell>
                  <TableCell>
                    {formatCurrency(
                      filteredAndSortedHoldings.reduce(
                        (sum, holding) => sum + holding.totalInvestment,
                        0
                      )
                    )}
                  </TableCell>
                  <TableCell>
                    {(() => {
                      // Calculate total gain/loss
                      const totalGainLoss = filteredAndSortedHoldings.reduce((sum, holding) => {
                        const sellValue = holding.currentPrice * (1 - (commission || 0) / 100) * holding.shareCount;
                        return sum + (sellValue - holding.totalInvestment);
                      }, 0);
                      
                      const isPositive = totalGainLoss >= 0;
                      
                      return (
                        <span className={isPositive ? "text-green-600" : "text-red-600"}>
                          {formatCurrency(Math.abs(totalGainLoss))}
                        </span>
                      );
                    })()}
                  </TableCell>
                  <TableCell>—</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Buy Modal */}
      <Dialog open={isBuyModalOpen} onOpenChange={setIsBuyModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Buy Shares</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleBuySubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tradingCode">Trading Code</Label>
              <Select
                value={buyFormData.tradingCodeId}
                onValueChange={handleTradingCodeSelect}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a trading code" />
                </SelectTrigger>
                <SelectContent>
                  {tradingCodes.map((code) => (
                    <SelectItem key={code.id} value={code.id.toString()}>
                      {code.code} - {code.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="buyPrice">Buy Price (BDT)</Label>
              <Input
                id="buyPrice"
                name="buyPrice"
                type="number"
                step="0.01"
                min="0"
                value={buyFormData.buyPrice}
                onChange={handleBuyInputChange}
              />
              {commission !== null &&
                commission > 0 &&
                buyFormData.buyPrice && (
                  <p className="text-sm text-muted-foreground">
                    Final price per unit:{" "}
                    {formatCurrency(
                      parseFloat(buyFormData.buyPrice) * (1 + commission / 100)
                    )}
                  </p>
                )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="unitCount">Unit Count</Label>
              <Input
                id="unitCount"
                name="unitCount"
                type="number"
                min="1"
                step="1"
                value={buyFormData.unitCount}
                onChange={handleBuyInputChange}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsBuyModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isFormSubmitting}>
                {isFormSubmitting ? "Adding..." : "Add to Portfolio"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Sell Modal */}
      <Dialog open={isSellModalOpen} onOpenChange={setIsSellModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sell Shares</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSellSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tradingCode">Trading Code</Label>
              <Select
                value={sellFormData.tradingCodeId}
                onValueChange={handleSellTradingCodeSelect}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a trading code" />
                </SelectTrigger>
                <SelectContent>
                  {/* Only show trading codes that the user owns */}
                  {holdings.map((holding) => {
                    // Find the trading code object
                    const tradingCode = tradingCodes.find(
                      (code) => code.code === holding.tradingCode
                    );
                    if (!tradingCode) return null;

                    return (
                      <SelectItem
                        key={tradingCode.id}
                        value={tradingCode.id.toString()}
                      >
                        {tradingCode.code} - {holding.shareCount} shares
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="sellPrice">Sell Price (BDT)</Label>
              <Input
                id="sellPrice"
                name="sellPrice"
                type="number"
                step="0.01"
                min="0"
                value={sellFormData.sellPrice}
                onChange={handleSellInputChange}
              />
              {commission !== null &&
                commission > 0 &&
                sellFormData.sellPrice && (
                  <p className="text-sm text-muted-foreground">
                    Final price per unit:{" "}
                    {formatCurrency(
                      parseFloat(sellFormData.sellPrice) *
                        (1 - commission / 100)
                    )}
                  </p>
                )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="unitCount">Unit Count</Label>
              <Input
                id="unitCount"
                name="unitCount"
                type="number"
                min="1"
                step="1"
                value={sellFormData.unitCount}
                onChange={handleSellInputChange}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsSellModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isFormSubmitting}>
                {isFormSubmitting ? "Processing..." : "Sell Shares"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}







