import { useSession } from "next-auth/react";
import { useEffect, useState, useRef, useCallback } from "react";
import { Session } from "next-auth";

// Global cache to prevent multiple components from triggering session fetches
let globalSessionCache: Session | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 60000; // 1 minute cache

// Custom hook with aggressive caching to prevent redundant calls
export function useAuth() {
  const { data: session, status, update } = useSession({
    required: false,
    // This option tells NextAuth to avoid refetching the session on every render
    onUnauthenticated: () => {},
  });
  
  const [cachedSession, setCachedSession] = useState(globalSessionCache || session);
  const componentName = useRef(new Error().stack?.split('\n')[2]?.trim() || 'unknown');
  
  // Update global cache when session changes
  useEffect(() => {
    if (session) {
      globalSessionCache = session;
      lastFetchTime = Date.now();
      setCachedSession(session);
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Session] Updated from ${componentName.current}`);
      }
    }
  }, [session]);
  
  // Force refresh session only when explicitly called and cache is stale
  const refreshSession = useCallback(async () => {
    const now = Date.now();
    if (now - lastFetchTime < 5000) {
      return cachedSession;
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Session] Forced refresh from ${componentName.current}`);
    }
    
    const updated = await update();
    lastFetchTime = now;
    globalSessionCache = updated;
    return updated;
  }, [update, cachedSession]);
  
  // Check if cache is stale and refresh if needed
  useEffect(() => {
    const now = Date.now();
    if (globalSessionCache && now - lastFetchTime > CACHE_DURATION) {
      refreshSession();
    }
  }, [refreshSession]);
  
  return { 
    session: cachedSession, 
    status: cachedSession ? "authenticated" : status,
    isAuthenticated: !!cachedSession,
    isLoading: status === "loading" && !cachedSession,
    refreshSession
  };
}




