"use client";

import DashboardLayout from "@/components/dashboard-layout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useLivePrices } from "@/hooks/use-live-prices";
import { useToast } from "@/hooks/use-toast";
import { RefreshCw } from "lucide-react";
import { useSession } from "next-auth/react";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import PortfolioTable, {
  PortfolioHolding,
} from "../../components/portfolio/PortfolioTable";

// Market status indicator component with tooltip
const MarketStatusIndicator = React.memo(({ isOpen }: { isOpen: boolean }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <span
          className={`ml-2 inline-block h-3 w-3 rounded-full ${
            isOpen ? "bg-green-500" : "bg-red-500"
          } cursor-help`}
          aria-label={`Market is ${isOpen ? "open" : "closed"}`}
        />
      </TooltipTrigger>
      <TooltipContent>
        <p>Market is {isOpen ? "open" : "closed"}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
));

MarketStatusIndicator.displayName = "MarketStatusIndicator";

export default function DashboardPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);

  // Cache market status to avoid excessive re-renders
  const [marketOpen, setMarketOpen] = useState<boolean>(false);

  // Store portfolio data in refs to avoid re-renders
  const portfolioRef = useRef<PortfolioHolding[]>([]);
  // Create a state for UI rendering only
  const [portfolioForDisplay, setPortfolioForDisplay] = useState<
    PortfolioHolding[]
  >([]);

  // Use a ref to track if we've already fetched data
  const initialFetchDone = useRef(false);

  // Use a ref to store trading codes to avoid dependency changes
  const tradingCodesRef = useRef<string[]>([]);

  // Track polling setup status to prevent duplicate setups/teardowns
  const isPollingSetupRef = useRef(false);

  // Set up dashboard active only once on component mount
  const setupComplete = useRef(false);

  // Use the live prices hook
  const {
    livePrices,
    isLoading: livePricesLoading,
    setDashboardActive,
    refreshPrices,
    isMarketOpen,
  } = useLivePrices();

  // Memoize the livePrices object to prevent unnecessary re-renders
  const livePricesMemo = useMemo(
    () => livePrices,
    [Object.keys(livePrices).join(",")]
  );

  // Add state for investment amount
  const [investmentAmount, setInvestmentAmount] = useState(0);

  // Check market status periodically
  useEffect(() => {
    // Initial check
    setMarketOpen(isMarketOpen());

    // Update every minute
    const statusInterval = setInterval(() => {
      setMarketOpen(isMarketOpen());
    }, 60000);

    return () => clearInterval(statusInterval);
  }, [isMarketOpen]);

  // One-time setup effect - run only on mount and unmount
  useEffect(() => {
    console.log("Dashboard component mounted");

    // Cleanup on unmount
    return () => {
      console.log("Dashboard component unmounting - final cleanup");
      setDashboardActive(false);
      isPollingSetupRef.current = false;
    };
  }, []); // Empty dependency array - run only on mount/unmount

  // Separated effect for updating trading codes when portfolio changes
  useEffect(() => {
    // Skip if no portfolio
    if (portfolioForDisplay.length === 0) return;

    // Extract trading codes from portfolio
    const codes = portfolioForDisplay.map((item) => item.tradingCode);

    // Only update reference if codes have changed
    const codesJson = JSON.stringify(codes.sort());
    const currentCodesJson = JSON.stringify(tradingCodesRef.current.sort());

    if (codesJson !== currentCodesJson) {
      console.log("Trading codes changed:", codes);
      tradingCodesRef.current = codes;

      // Update polling if already set up
      if (!isPollingSetupRef.current) {
        console.log("Setting up initial dashboard polling for:", codes);
        setDashboardActive(true, codes);
        isPollingSetupRef.current = true;
      } else {
        console.log("Updating dashboard polling for new trading codes:", codes);
        setDashboardActive(true, codes);
      }
    }
  }, [portfolioForDisplay.length, setDashboardActive]); // Only depend on length, not the entire array

  // Update portfolio from live prices without triggering re-renders
  useEffect(() => {
    // Skip if no data
    if (
      Object.keys(livePricesMemo).length === 0 ||
      portfolioRef.current.length === 0
    )
      return;

    // Create an updated portfolio with live prices
    const updatedPortfolio = portfolioRef.current.map((holding) => {
      const livePrice = livePricesMemo[holding.tradingCode]?.price;
      if (livePrice) {
        return {
          ...holding,
          currentPrice: livePrice,
          isLivePrice: true,
          previousPrice: livePricesMemo[holding.tradingCode]?.previous_price,
          changeDirection:
            livePricesMemo[holding.tradingCode]?.change_direction,
          changePercent: livePricesMemo[holding.tradingCode]?.change_percent,
        };
      }
      return holding;
    });

    // Store the updated data in the ref without triggering re-renders
    portfolioRef.current = updatedPortfolio;

    // Only update the display state (which causes renders) if there's a material change
    const shouldUpdate =
      JSON.stringify(portfolioForDisplay) !== JSON.stringify(updatedPortfolio);

    if (shouldUpdate) {
      console.log("Updating display portfolio with live prices");
      setPortfolioForDisplay(updatedPortfolio);
    }
  }, [livePricesMemo]); // Only depend on memoized prices

  // Fetch portfolio data with useCallback to maintain stable reference
  const fetchPortfolioData = useCallback(async () => {
    // Skip if already fetching
    if (isFetchingRef.current) {
      return;
    }

    isFetchingRef.current = true;
    setIsLoading(true);

    try {
      // Fetch portfolio entries
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/entries`,
        {
          headers: {
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const entries = await response.json();

        // Process entries to match PortfolioHolding format
        const processedEntries = processPortfolioEntries(entries);

        // Update the ref first to avoid re-renders
        portfolioRef.current = processedEntries;

        // Update the display state
        setPortfolioForDisplay(processedEntries);
      } else {
        console.error("Failed to fetch portfolio data:", await response.text());
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to fetch portfolio data",
        });
      }
    } catch (error) {
      console.error("Error fetching portfolio data:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while fetching portfolio data",
      });
    } finally {
      setIsLoading(false);
      isFetchingRef.current = false;
    }
  }, [session, toast]);

  // Use a ref to track if a fetch is in progress to prevent duplicate calls
  const isFetchingRef = useRef(false);

  // Fetch portfolio data on component mount
  useEffect(() => {
    if (session?.user?.accessToken && !initialFetchDone.current) {
      fetchPortfolioData();
      initialFetchDone.current = true;
    }
  }, [session, fetchPortfolioData]);

  // Process portfolio entries - memoized to maintain reference stability
  const processPortfolioEntries = useCallback(
    (entries: any[]): PortfolioHolding[] => {
      if (!entries || entries.length === 0) {
        return [];
      }

      // Filter out entries without trading_code information
      // More permissive check to handle different API response structures
      const validEntries = entries.filter((entry) => {
        // Check if entry has trading_code object with code property
        const hasTradingCodeObject =
          entry.trading_code && entry.trading_code.code;

        // Check if entry has trading_code_id and we need to look up the code
        const hasTradingCodeId = entry.trading_code_id;

        return hasTradingCodeObject || hasTradingCodeId;
      });

      if (validEntries.length === 0) {
        return [];
      }

      // Group entries by trading code
      const groupedEntries = validEntries.reduce((acc, entry) => {
        // Get trading code - either from the object or use the ID as fallback
        let tradingCode = "Unknown";

        if (entry.trading_code && entry.trading_code.code) {
          tradingCode = entry.trading_code.code;
        } else if (entry.trading_code_id) {
          tradingCode = `Code-${entry.trading_code_id}`;
        }

        if (!acc[tradingCode]) {
          acc[tradingCode] = [];
        }
        acc[tradingCode].push(entry);
        return acc;
      }, {});

      // Convert grouped entries to PortfolioHolding format
      return (
        Object.entries(groupedEntries)
          .map((entry) => {
            const tradingCode = entry[0];
            const entries = entry[1] as any[];

            // Sort entries by date for chronological processing
            const sortedEntries = [...entries].sort((a, b) => {
              const dateA = new Date(a.transaction_date || a.buy_date);
              const dateB = new Date(b.transaction_date || b.buy_date);
              return dateA.getTime() - dateB.getTime();
            });

            // Track total shares and average price
            let totalShares = 0;
            let averagePrice = 0;
            let lastTransactionDate =
              sortedEntries[0]?.transaction_date || sortedEntries[0]?.buy_date;

            // Process each transaction chronologically
            sortedEntries.forEach((entry) => {
              const transactionType = entry.transaction_type || "buy";
              const price = entry.transaction_price || entry.buy_price;
              const date = entry.transaction_date || entry.buy_date;

              // Update last transaction date
              if (new Date(date) > new Date(lastTransactionDate)) {
                lastTransactionDate = date;
              }

              if (transactionType === "buy") {
                // Calculate new average price
                if (totalShares === 0) {
                  // First purchase
                  averagePrice = price;
                  totalShares = entry.unit_count;
                } else {
                  // Calculate weighted average
                  const totalValue = totalShares * averagePrice;
                  const newValue = entry.unit_count * price;
                  totalShares += entry.unit_count;
                  averagePrice = (totalValue + newValue) / totalShares;
                }
              } else if (transactionType === "sell") {
                // Reduce share count but maintain average price
                totalShares -= entry.unit_count;
                // Reset if all shares are sold
                if (totalShares <= 0) {
                  totalShares = 0;
                  averagePrice = 0;
                }
              }
            });

            // Skip if no shares are left
            if (totalShares <= 0) {
              return null;
            }

            const totalInvestment = totalShares * averagePrice;

            // Use live price if available, otherwise use average price
            const livePrice = livePricesMemo[tradingCode]?.price;
            const currentPrice = livePrice || averagePrice;
            const isLivePrice = !!livePrice;

            // Include price change data if available
            const previousPrice = livePricesMemo[tradingCode]?.previous_price;
            const changeDirection =
              livePricesMemo[tradingCode]?.change_direction;
            const changePercent = livePricesMemo[tradingCode]?.change_percent;

            return {
              tradingCode,
              currentPrice,
              shareCount: totalShares,
              avgPrice: averagePrice,
              totalInvestment,
              lastBuyDate: lastTransactionDate,
              isLivePrice,
              previousPrice,
              changeDirection,
              changePercent,
            };
          })
          // Filter out null entries (fully sold positions)
          .filter((entry) => entry !== null)
      );
    },
    [livePricesMemo]
  );

  // Stable refresh handler with useCallback
  const handleRefreshPrices = useCallback(() => {
    if (tradingCodesRef.current.length > 0) {
      refreshPrices(tradingCodesRef.current);
    }
  }, [refreshPrices]);

  // Memoize the last updated time calculation
  const lastUpdatedTime = useMemo(() => {
    if (Object.keys(livePricesMemo).length === 0) return null;

    return new Date(
      Math.max(
        ...Object.values(livePricesMemo).map((price) =>
          new Date(price.last_updated).getTime()
        )
      )
    ).toLocaleTimeString();
  }, [livePricesMemo]);

  // Fetch portfolio settings including investment amount
  const fetchPortfolioSettings = useCallback(async () => {
    if (!session?.user?.accessToken) return;
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`,
        {
          headers: {
            Authorization: `Bearer ${session.user.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setInvestmentAmount(data.investment_amount || 0);
      }
    } catch (error) {
      console.error("Error fetching portfolio settings:", error);
    }
  }, [session]);

  // Call this in useEffect along with other initial data fetching
  useEffect(() => {
    if (session?.user?.accessToken) {
      fetchPortfolioData();
      fetchPortfolioSettings();
    }
  }, [session, fetchPortfolioData, fetchPortfolioSettings]);

  return (
    <DashboardLayout>
      <div>
        {/* Portfolio Section */}
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-gray-500">
            {lastUpdatedTime && (
              <span className="flex items-center">
                Last updated: {lastUpdatedTime}
                <MarketStatusIndicator isOpen={marketOpen} />
              </span>
            )}
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={handleRefreshPrices}
                  size="sm"
                  className="px-2"
                  disabled={
                    !marketOpen && Object.keys(livePricesMemo).length > 0
                  }
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{marketOpen ? "Refresh Live Prices" : "Market is closed"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <PortfolioTable
          holdings={portfolioForDisplay}
          isLoading={isLoading}
          onEntryAdded={fetchPortfolioData}
          investmentAmount={investmentAmount}
        />
      </div>
    </DashboardLayout>
  );
}

