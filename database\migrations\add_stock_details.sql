-- Add stock_details table for additional stock information from ecosoftbd.com
CREATE TABLE IF NOT EXISTS dse_schema.stock_details (
    id SERIAL PRIMARY KEY,
    trading_code_id INTEGER NOT NULL REFERENCES dse_schema.trading_codes(id) ON DELETE CASCADE,
    open_price FLOAT,
    pe_ratio FLOAT,
    record_date DATE,
    dividend_info VARCHAR(255),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one record per trading code
    UNIQUE(trading_code_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stock_details_trading_code_id ON dse_schema.stock_details(trading_code_id);
CREATE INDEX IF NOT EXISTS idx_stock_details_last_updated ON dse_schema.stock_details(last_updated);

-- Add comments to explain the table
COMMENT ON TABLE dse_schema.stock_details IS 'Additional stock information fetched from ecosoftbd.com';
COMMENT ON COLUMN dse_schema.stock_details.open_price IS 'Opening price of the stock';
COMMENT ON COLUMN dse_schema.stock_details.pe_ratio IS 'Price to Earnings ratio';
COMMENT ON COLUMN dse_schema.stock_details.record_date IS 'Record date for dividends';
COMMENT ON COLUMN dse_schema.stock_details.dividend_info IS 'Dividend information';
COMMENT ON COLUMN dse_schema.stock_details.last_updated IS 'When this data was last fetched from ecosoftbd.com';
