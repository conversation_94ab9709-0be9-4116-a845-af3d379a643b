import { useSession } from "next-auth/react";
import { useCallback, useEffect, useState } from "react";
import { useToast } from "./use-toast";

// Type for stock details data
export interface StockDetails {
  id: number;
  trading_code_id: number;
  open_price?: number;
  pe_ratio?: number;
  record_date?: string;
  dividend_info?: string;
  last_updated: string;
  created_at: string;
  trading_code?: string;
}

export interface StockDetailsMap {
  [tradingCode: string]: StockDetails;
}

export function useStockDetails() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [stockDetails, setStockDetails] = useState<StockDetailsMap>({});
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  const fetchStockDetails = useCallback(
    async (tradingCodes?: string[]) => {
      if (!session?.user?.accessToken) return;

      setIsLoading(true);

      try {
        const url = tradingCodes && tradingCodes.length > 0
          ? `${process.env.NEXT_PUBLIC_API_URL}/stock-details?codes=${tradingCodes.join(",")}`
          : `${process.env.NEXT_PUBLIC_API_URL}/stock-details`;

        console.log(`Fetching stock details from: ${url}`);

        const response = await fetch(url, {
          headers: {
            Authorization: `Bearer ${session.user.accessToken}`,
          },
        });

        if (response.ok) {
          const data: StockDetails[] = await response.json();
          console.log("Fetched stock details:", data);

          // Convert array to map keyed by trading code
          const detailsMap: StockDetailsMap = {};
          data.forEach((detail) => {
            if (detail.trading_code) {
              detailsMap[detail.trading_code] = detail;
            }
          });

          setStockDetails(detailsMap);

          // Update last updated time
          if (data.length > 0) {
            const mostRecent = data.reduce((latest, current) => {
              return new Date(current.last_updated) > new Date(latest.last_updated)
                ? current
                : latest;
            });
            setLastUpdated(mostRecent.last_updated);
          }
        } else {
          console.error("Failed to fetch stock details:", await response.text());
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to fetch stock details",
          });
        }
      } catch (error) {
        console.error("Error fetching stock details:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "An error occurred while fetching stock details",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [session, toast]
  );

  const getStockDetailsStatus = useCallback(async () => {
    if (!session?.user?.accessToken) return null;

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/stock-details/status`,
        {
          headers: {
            Authorization: `Bearer ${session.user.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const status = await response.json();
        return status;
      }
    } catch (error) {
      console.error("Error fetching stock details status:", error);
    }

    return null;
  }, [session]);

  const syncStockDetails = useCallback(
    async (tradingCodes?: string[]) => {
      if (!session?.user?.accessToken) return;

      try {
        const url = tradingCodes && tradingCodes.length > 0
          ? `${process.env.NEXT_PUBLIC_API_URL}/stock-details/sync?codes=${tradingCodes.join(",")}`
          : `${process.env.NEXT_PUBLIC_API_URL}/stock-details/sync`;

        const response = await fetch(url, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${session.user.accessToken}`,
          },
        });

        if (response.ok) {
          const result = await response.json();
          toast({
            title: "Stock Details Sync",
            description: result.message,
          });

          // Refresh data after sync
          setTimeout(() => {
            fetchStockDetails(tradingCodes);
          }, 2000);
        } else {
          const error = await response.json();
          toast({
            variant: "destructive",
            title: "Sync Failed",
            description: error.detail || "Failed to sync stock details",
          });
        }
      } catch (error) {
        console.error("Error syncing stock details:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "An error occurred while syncing stock details",
        });
      }
    },
    [session, toast, fetchStockDetails]
  );

  // Check if data is stale (older than 24 hours)
  const isDataStale = useCallback(() => {
    if (!lastUpdated) return true;

    const now = new Date();
    const lastUpdateTime = new Date(lastUpdated);
    const hoursSinceUpdate = (now.getTime() - lastUpdateTime.getTime()) / (1000 * 60 * 60);

    return hoursSinceUpdate > 24;
  }, [lastUpdated]);

  // Auto-fetch stock details when component mounts
  useEffect(() => {
    if (session?.user?.accessToken) {
      fetchStockDetails();
    }
  }, [session?.user?.accessToken]);

  return {
    stockDetails,
    isLoading,
    lastUpdated,
    fetchStockDetails,
    getStockDetailsStatus,
    syncStockDetails,
    isDataStale: isDataStale(),
  };
}
