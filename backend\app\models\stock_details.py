from datetime import datetime

from app.db.base_class import Base
from sqlalchemy import (Column, Date, DateTime, Float, ForeignKey, Integer,
                        String)
from sqlalchemy.orm import relationship


class StockDetails(Base):
    __tablename__ = 'stock_details'
    __table_args__ = (
        {'schema': 'dse_schema'},
    )

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('dse_schema.users.id'), nullable=False)
    trading_code_id = Column(Integer, ForeignKey('dse_schema.trading_codes.id'), nullable=False)
    open_price = Column(Float, nullable=True)
    pe_ratio = Column(Float, nullable=True)
    record_date = Column(Date, nullable=True)
    dividend_info = Column(String(255), nullable=True)
    last_updated = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)

    # Relationships
    trading_code = relationship("TradingCode", back_populates="stock_details", lazy="selectin")
    user = relationship("User", lazy="selectin")
