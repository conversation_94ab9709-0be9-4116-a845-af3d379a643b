import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from app.api.deps import get_current_user, get_current_active_admin, get_db
from app.core.ecosoft_scraper import fetch_and_update_stock_details
from app.models.stock_details import StockDetails
from app.models.trading_code import TradingCode
from app.models.user import User
from app.schemas.stock_details import StockDetailsResponse, StockDetailsWithTradingCode
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("", response_model=List[StockDetailsWithTradingCode])
async def get_stock_details(
    codes: str = Query(None, description="Comma-separated list of trading codes"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get stock details for specific trading codes or all trading codes
    """
    try:
        if codes:
            # Parse the comma-separated list of trading codes
            trading_codes = [code.strip().upper() for code in codes.split(",")]
            logger.info(f"Fetching stock details for specific codes: {trading_codes}")
            
            # Build query for specific trading codes
            query = (
                select(StockDetails, TradingCode.code)
                .join(TradingCode, StockDetails.trading_code_id == TradingCode.id)
                .where(TradingCode.code.in_(trading_codes))
            )
        else:
            # Get all stock details
            query = (
                select(StockDetails, TradingCode.code)
                .join(TradingCode, StockDetails.trading_code_id == TradingCode.id)
            )

        result = await db.execute(query)
        stock_details_with_codes = result.all()

        # Convert to response format
        response_data = []
        for stock_detail, trading_code in stock_details_with_codes:
            response_data.append(
                StockDetailsWithTradingCode(
                    id=stock_detail.id,
                    trading_code_id=stock_detail.trading_code_id,
                    open_price=stock_detail.open_price,
                    pe_ratio=stock_detail.pe_ratio,
                    record_date=stock_detail.record_date,
                    dividend_info=stock_detail.dividend_info,
                    last_updated=stock_detail.last_updated,
                    created_at=stock_detail.created_at,
                    trading_code=trading_code
                )
            )

        return response_data

    except Exception as e:
        logger.error(f"Error fetching stock details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch stock details"
        )


@router.post("/sync", status_code=status.HTTP_200_OK)
async def sync_stock_details(
    codes: str = Query(None, description="Comma-separated list of trading codes to sync"),
    background_tasks: BackgroundTasks = None,
    current_admin = Depends(get_current_active_admin),
    db: AsyncSession = Depends(get_db),
):
    """
    Sync stock details from ecosoftbd.com (Admin only)
    """
    try:
        if codes:
            trading_codes = [code.strip().upper() for code in codes.split(",")]
            logger.info(f"Admin sync requested for specific codes: {trading_codes}")
        else:
            trading_codes = None
            logger.info("Admin sync requested for all trading codes")

        if background_tasks:
            # Run in background
            background_tasks.add_task(fetch_and_update_stock_details, trading_codes)
            return {
                "message": "Stock details sync started in background",
                "codes": trading_codes or "all"
            }
        else:
            # Run synchronously (for testing)
            results = await fetch_and_update_stock_details(trading_codes)
            return {
                "message": "Stock details sync completed",
                "results": results
            }

    except Exception as e:
        logger.error(f"Error syncing stock details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to sync stock details"
        )


@router.get("/status")
async def get_sync_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get the status of stock details (when they were last updated)
    """
    try:
        # Get the most recent update time
        query = select(StockDetails.last_updated).order_by(StockDetails.last_updated.desc()).limit(1)
        result = await db.execute(query)
        last_updated = result.scalar_one_or_none()

        # Count total stock details
        count_query = select(StockDetails.id)
        count_result = await db.execute(count_query)
        total_count = len(count_result.all())

        # Check if data is stale (older than 24 hours)
        is_stale = False
        if last_updated:
            now = datetime.now()
            age = now - last_updated.replace(tzinfo=None)
            is_stale = age > timedelta(hours=24)

        return {
            "last_updated": last_updated.isoformat() if last_updated else None,
            "total_records": total_count,
            "is_stale": is_stale,
            "needs_update": is_stale or total_count == 0
        }

    except Exception as e:
        logger.error(f"Error getting sync status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sync status"
        )
