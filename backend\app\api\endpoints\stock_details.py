import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from app.api.deps import get_current_active_admin, get_current_user, get_db
from app.models.stock_details import StockDetails
from app.models.trading_code import TradingCode
from app.models.user import User
from app.schemas.stock_details import (StockDetailsResponse,
                                       StockDetailsWithTradingCode)
from fastapi import (APIRouter, BackgroundTasks, Depends, HTTPException, Query,
                     status)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("", response_model=List[StockDetailsWithTradingCode])
async def get_stock_details(
    codes: str = Query(None, description="Comma-separated list of trading codes"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get stock details for current user's portfolio trading codes
    """
    try:
        # Always filter by current user
        base_query = (
            select(StockDetails, TradingCode.code)
            .join(TradingCode, StockDetails.trading_code_id == TradingCode.id)
            .where(StockDetails.user_id == current_user.id)
        )

        if codes:
            # Parse the comma-separated list of trading codes
            trading_codes = [code.strip().upper() for code in codes.split(",")]
            logger.info(f"Fetching stock details for user {current_user.id}, codes: {trading_codes}")

            # Add filter for specific trading codes
            query = base_query.where(TradingCode.code.in_(trading_codes))
        else:
            # Get all stock details for this user
            logger.info(f"Fetching all stock details for user {current_user.id}")
            query = base_query

        result = await db.execute(query)
        stock_details_with_codes = result.all()

        # Convert to response format
        response_data = []
        for stock_detail, trading_code in stock_details_with_codes:
            response_data.append(
                StockDetailsWithTradingCode(
                    id=stock_detail.id,
                    user_id=stock_detail.user_id,
                    trading_code_id=stock_detail.trading_code_id,
                    open_price=stock_detail.open_price,
                    pe_ratio=stock_detail.pe_ratio,
                    record_date=stock_detail.record_date,
                    dividend_info=stock_detail.dividend_info,
                    last_updated=stock_detail.last_updated,
                    created_at=stock_detail.created_at,
                    trading_code=trading_code
                )
            )

        return response_data

    except Exception as e:
        logger.error(f"Error fetching stock details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch stock details"
        )


@router.post("/sync", status_code=status.HTTP_200_OK)
async def sync_stock_details(
    background_tasks: BackgroundTasks = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Sync stock details for current user's portfolio holdings
    """
    try:
        logger.info(f"Stock details sync requested for user {current_user.id}")

        # Import here to avoid circular imports
        from app.services.portfolio_stock_details import \
            sync_user_stock_details

        if background_tasks:
            # Run in background
            background_tasks.add_task(sync_user_stock_details, db, current_user.id)
            return {
                "message": "Stock details sync started in background for your portfolio",
                "user_id": current_user.id
            }
        else:
            # Run synchronously
            results = await sync_user_stock_details(db, current_user.id)
            return results

    except Exception as e:
        logger.error(f"Error syncing stock details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to sync stock details"
        )


@router.get("/status")
async def get_sync_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get the status of stock details for current user's portfolio
    """
    try:
        # Get the most recent update time for this user
        query = (
            select(StockDetails.last_updated)
            .where(StockDetails.user_id == current_user.id)
            .order_by(StockDetails.last_updated.desc())
            .limit(1)
        )
        result = await db.execute(query)
        last_updated = result.scalar_one_or_none()

        # Count total stock details for this user
        count_query = select(StockDetails.id).where(StockDetails.user_id == current_user.id)
        count_result = await db.execute(count_query)
        total_count = len(count_result.all())

        # Check if data is stale (older than 24 hours)
        is_stale = False
        if last_updated:
            now = datetime.now()
            age = now - last_updated.replace(tzinfo=None)
            is_stale = age > timedelta(hours=24)

        return {
            "user_id": current_user.id,
            "last_updated": last_updated.isoformat() if last_updated else None,
            "total_records": total_count,
            "is_stale": is_stale,
            "needs_update": is_stale or total_count == 0
        }

    except Exception as e:
        logger.error(f"Error getting sync status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sync status"
        )
