import { useSession } from "next-auth/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useToast } from "./use-toast";

// Type for live price data
export interface LivePrice {
  trading_code: string;
  price: number;
  last_updated: string;
  previous_price?: number;
  change_direction?: "up" | "down" | "none";
  change_percent?: number;
}

export function useLivePrices() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [livePrices, setLivePrices] = useState<Record<string, LivePrice>>(
    () => {
      // Try to load cached prices from localStorage on initial load
      if (typeof window !== "undefined") {
        try {
          const cachedData = localStorage.getItem("livePrices");
          const cachedTimestamp = localStorage.getItem("livePricesTimestamp");

          if (cachedData && cachedTimestamp) {
            const timestamp = parseInt(cachedTimestamp);
            const now = Date.now();
            const fiveMinutesInMs = 5 * 60 * 1000;

            // Only use cache if it's less than 5 minutes old
            if (now - timestamp < fiveMinutesInMs) {
              const parsedData = JSON.parse(cachedData);
              console.log("Using cached live prices");
              return parsedData;
            }
          }
        } catch (error) {
          console.error("Error loading cached prices:", error);
        }
      }
      return {};
    }
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isOnDashboard, setIsOnDashboard] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastFetchTimeRef = useRef<number>(0);
  const marketStatusRef = useRef<boolean | null>(null);
  const lastMarketCheckRef = useRef<number>(0);
  const initialFetchDoneRef = useRef<boolean>(false);
  const tradingCodesRef = useRef<string[]>([]);

  // Used to prevent redundant setup/teardown cycles
  const lastSetupRef = useRef<{ active: boolean; codes: string[] }>({
    active: false,
    codes: [],
  });

  // Update localStorage when livePrices change
  useEffect(() => {
    if (Object.keys(livePrices).length > 0) {
      try {
        localStorage.setItem("livePrices", JSON.stringify(livePrices));
        localStorage.setItem("livePricesTimestamp", Date.now().toString());
      } catch (error) {
        console.error("Error caching live prices:", error);
      }
    }
  }, [livePrices]);

  // Function to check if market is open (with caching)
  const isMarketOpen = useCallback((): boolean => {
    // Only recalculate market status every minute to avoid excessive calls
    const now = Date.now();
    if (
      marketStatusRef.current !== null &&
      now - lastMarketCheckRef.current < 60000
    ) {
      return marketStatusRef.current;
    }

    // Bangladesh timezone is GMT+6
    const currentDate = new Date();
    const bstOffset = 6 * 60; // 6 hours in minutes
    const utcMinutes =
      currentDate.getUTCHours() * 60 + currentDate.getUTCMinutes();
    const bstMinutes = (utcMinutes + bstOffset) % (24 * 60);
    const bstHour = Math.floor(bstMinutes / 60);
    const bstDay = currentDate.getUTCDay();

    // Market hours: 10:00 AM to 2:30 PM BST
    const isMarketHours =
      bstHour >= 10 &&
      (bstHour < 14 || (bstHour === 14 && bstMinutes % 60 <= 30));

    // Weekend in Bangladesh: Friday (5) and Saturday (6)
    const isWeekend = bstDay === 5 || bstDay === 6;

    const isOpen = isMarketHours && !isWeekend;

    // Cache the result
    marketStatusRef.current = isOpen;
    lastMarketCheckRef.current = now;

    console.log(
      `Market status check: ${isOpen ? "OPEN" : "CLOSED"} (BST: ${bstHour}:${
        bstMinutes % 60
      }, Day: ${bstDay})`
    );
    return isOpen;
  }, []);

  // Function to fetch live prices
  const fetchLivePrices = useCallback(
    async (tradingCodes: string[], forceRefresh = false) => {
      if (!session?.user?.accessToken || !tradingCodes.length) return;

      // Don't fetch too frequently (minimum 10 seconds between fetches)
      const now = Date.now();
      if (now - lastFetchTimeRef.current < 10000 && !forceRefresh) {
        console.log("Skipping fetch - too soon since last fetch");
        return;
      }

      // Don't fetch if market is closed, unless it's the initial fetch or forced refresh
      const marketOpen = isMarketOpen();
      const isInitialFetch = Object.keys(livePrices).length === 0;

      if (!marketOpen && !isInitialFetch && !forceRefresh) {
        console.log("Skipping fetch - market is closed and not initial fetch");
        return;
      }

      lastFetchTimeRef.current = now;
      setIsLoading(true);

      try {
        console.log(
          `Fetching prices for ${tradingCodes.join(", ")}${
            forceRefresh ? " (forced)" : ""
          }${isInitialFetch ? " (initial)" : ""}`
        );

        const response = await fetch(
          `${
            process.env.NEXT_PUBLIC_API_URL
          }/live-prices?codes=${tradingCodes.join(",")}`,
          {
            headers: {
              Authorization: `Bearer ${session.user.accessToken}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          console.log("Fetched live prices:", data);

          // Process the prices to add change indicators
          const processedPrices: Record<string, LivePrice> = {};

          Object.entries(data.prices || {}).forEach(
            ([code, priceData]: [string, any]) => {
              const currentPrice = priceData.price;
              const previousPrice = livePrices[code]?.price;

              let changeDirection: "up" | "down" | "none" = "none";
              let changePercent = 0;

              // Only calculate changes if we have previous price data
              if (previousPrice && previousPrice !== currentPrice) {
                changeDirection = currentPrice > previousPrice ? "up" : "down";
                changePercent =
                  ((currentPrice - previousPrice) / previousPrice) * 100;
              }

              processedPrices[code] = {
                ...priceData,
                previous_price: previousPrice || currentPrice,
                change_direction: changeDirection,
                change_percent: changePercent,
              };
            }
          );

          setLivePrices(processedPrices);
        } else {
          console.error("Failed to fetch live prices:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching live prices:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [session, livePrices, isMarketOpen]
  );

  // Function to manually refresh prices
  const refreshPrices = useCallback(
    async (tradingCodes: string[]) => {
      if (!tradingCodes.length) return;
      console.log("Manually refreshing prices for:", tradingCodes);
      await fetchLivePrices(tradingCodes, true); // Force refresh
      toast({
        title: "Refreshing Prices",
        description: "Live prices have been updated",
      });
    },
    [fetchLivePrices, toast]
  );

  // Function to start polling for specific trading codes
  const startPolling = useCallback(
    (tradingCodes: string[]) => {
      if (!tradingCodes.length) return;

      // Clear any existing interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }

      // Only do initial fetch if it hasn't been done yet
      if (!initialFetchDoneRef.current) {
        console.log("Initial fetch for trading codes:", tradingCodes);
        fetchLivePrices(tradingCodes, true);
        initialFetchDoneRef.current = true;
      }

      // Set up interval with intelligent polling
      pollingIntervalRef.current = setInterval(() => {
        const marketOpen = isMarketOpen();
        if (marketOpen) {
          console.log("Polling for live prices during market hours...");
          fetchLivePrices(tradingCodes);
        } else {
          console.log("Market closed, skipping scheduled price update");
        }
      }, 5 * 60 * 1000); // 5 minutes

      console.log(`Started polling for ${tradingCodes.length} trading codes`);
    },
    [fetchLivePrices, isMarketOpen]
  );

  // Function to notify when entering/leaving dashboard
  const setDashboardActive = useCallback(
    (active: boolean, tradingCodes: string[] = []) => {
      // Sort both arrays for consistent comparison
      const sortedNewCodes = [...tradingCodes].sort();
      const sortedCurrentCodes = [...lastSetupRef.current.codes].sort();
      
      // Use JSON.stringify for deep comparison of arrays
      const codesStr = JSON.stringify(sortedNewCodes);
      const lastCodesStr = JSON.stringify(sortedCurrentCodes);

      // Skip if there's no change in state and trading codes
      if (active === lastSetupRef.current.active && codesStr === lastCodesStr) {
        console.log("Skipping dashboard activation - no changes detected");
        return;
      }

      // Update last setup ref to avoid redundant operations
      lastSetupRef.current = {
        active,
        codes: [...tradingCodes],
      };

      // Store current trading codes
      tradingCodesRef.current = [...tradingCodes];

      // Only update state if it's actually changing
      if (active !== isOnDashboard) {
        setIsOnDashboard(active);
      }

      if (active && tradingCodes.length > 0) {
        // Clear any existing interval first
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }

        console.log(
          `${isOnDashboard ? "Restarting" : "Starting"} live price polling for: ${tradingCodes.join(", ")}`
        );
        startPolling(tradingCodes);
      } else if (!active) {
        console.log("Stopping live price polling");
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        // Reset initial fetch flag when leaving dashboard
        initialFetchDoneRef.current = false;
      }
    },
    [startPolling, isOnDashboard]
  );

  return {
    livePrices,
    isLoading,
    setDashboardActive,
    refreshPrices,
    startPolling,
    isMarketOpen,
  };
}

