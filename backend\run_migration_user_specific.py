import sys
sys.path.append('.')
import asyncio
from app.db.session import engine
from sqlalchemy import text

async def run_migration():
    # Split migration into separate statements
    statements = [
        # First, we need to add the column as nullable temporarily
        "ALTER TABLE dse_schema.stock_details ADD COLUMN user_id INTEGER",
        
        # Add the foreign key constraint
        "ALTER TABLE dse_schema.stock_details ADD CONSTRAINT fk_stock_details_user_id FOREIGN KEY (user_id) REFERENCES dse_schema.users(id) ON DELETE CASCADE",
        
        # Since we don't have any existing data, we can make it NOT NULL directly
        "ALTER TABLE dse_schema.stock_details ALTER COLUMN user_id SET NOT NULL",
        
        # Drop the old unique constraint
        "ALTER TABLE dse_schema.stock_details DROP CONSTRAINT IF EXISTS stock_details_trading_code_id_key",
        
        # Add new unique constraint for user_id + trading_code_id combination
        "ALTER TABLE dse_schema.stock_details ADD CONSTRAINT stock_details_user_trading_code_unique UNIQUE(user_id, trading_code_id)",
        
        # Create index for user_id
        "CREATE INDEX IF NOT EXISTS idx_stock_details_user_id ON dse_schema.stock_details(user_id)",
        
        # Add comment
        "COMMENT ON COLUMN dse_schema.stock_details.user_id IS 'User who owns this stock in their portfolio'"
    ]
    
    try:
        async with engine.begin() as conn:
            for i, statement in enumerate(statements):
                print(f"Executing statement {i+1}/{len(statements)}")
                try:
                    await conn.execute(text(statement))
                    print(f"✓ Statement {i+1} executed successfully")
                except Exception as e:
                    print(f"✗ Statement {i+1} failed: {e}")
                    # Continue with other statements
        print('Migration executed successfully')
    except Exception as e:
        print(f'Migration failed: {e}')

if __name__ == "__main__":
    asyncio.run(run_migration())
